# Generated by Cargo
/target/

# Generated by wasm-pack
/pkg/

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

# IDE specific files
.vscode/
.idea/
*.iml

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Generated files
*.generated.*
