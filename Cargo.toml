[package]
name = "mdq-wasm"
version = "0.1.0"
edition = "2021"
description = "WebAssembly bindings for mdq"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
mdq = { git = "https://github.com/yshavit/mdq", branch = "refactor-cli-opt-02" }
js-sys = "0.3"
web-sys = { version = "0.3", features = ["console"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1.0"
wasm-bindgen = "0.2"
console_error_panic_hook = { version = "0.1.7", optional = true }

[features]
default = ["console_error_panic_hook"]
