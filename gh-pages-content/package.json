{"name": "mdq-wasm-demo", "version": "1.0.0", "description": "TypeScript-powered MDQ WebAssembly Demo", "type": "module", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npx http-server -p 8080", "dev": "concurrently \"npm run build:watch\" \"npm run serve\"", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit"}, "devDependencies": {"typescript": "^5.3.0", "concurrently": "^8.2.0", "rimraf": "^5.0.0"}, "files": ["dist/**/*", "pkg/**/*", "index.html"]}