<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDQ - Markdown Query Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .editor-container {
            height: 400px;
            border-radius: 4px;
            overflow: hidden;
        }
        .result-container {
            height: 400px;
            border-radius: 4px;
            overflow: auto;
            background-color: white;
            border: 1px solid #dee2e6;
            padding: 10px;
        }
        .header {
            margin-bottom: 30px;
        }
        .form-label {
            font-weight: 600;
        }
        .example-selector {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: #e9ecef;
            margin-right: 5px;
            display: inline-block;
            margin-bottom: 5px;
        }
        .example-selector:hover {
            background-color: #dee2e6;
        }
        #loading {
            display: none;
            margin-top: 10px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MDQ - Markdown Query Demo</h1>
            <p class="lead">Select specific elements in a Markdown document using mdq selectors</p>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="markdown-input" class="form-label">Markdown Input</label>
                    <div id="markdown-editor" class="editor-container"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="result" class="form-label">Result</label>
                    <div id="result" class="result-container"></div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="selector" class="form-label">MDQ Selector</label>
                    <input type="text" class="form-control" id="selector" placeholder="Enter selector (e.g., '# heading')">
                </div>
                <div class="mb-3">
                    <label class="form-label">Example Selectors</label>
                    <div>
                        <span class="example-selector" data-selector="# heading">Headings</span>
                        <span class="example-selector" data-selector="P: text">Paragraphs</span>
                        <span class="example-selector" data-selector="- list">Unordered lists</span>
                        <span class="example-selector" data-selector="- [ ] task">Task items</span>
                        <span class="example-selector" data-selector="```">Code blocks</span>
                        <span class="example-selector" data-selector="[link]()">Links</span>
                        <span class="example-selector" data-selector="> quote">Blockquotes</span>
                        <span class="example-selector" data-selector=":-: * :-:">Tables</span>
                    </div>
                    <div class="mt-2">
                        <a href="https://github.com/yshavit/mdq/wiki/Full-User-Manual" target="_blank" class="text-decoration-none">
                            View Full Selector Syntax Documentation →
                        </a>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Output Format</label>
                    <select class="form-select" id="output-format">
                        <option value="markdown">Markdown</option>
                        <option value="json">JSON</option>
                    </select>
                </div>
                <div class="mb-3">
                    <button id="process-btn" class="btn btn-primary">Process</button>
                    <div id="loading" class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">About MDQ</label>
                    <div class="card">
                        <div class="card-body">
                            <p>MDQ is a command-line tool that lets you select specific elements from a Markdown document using a simple query language.</p>
                            <p>This page uses WebAssembly to run the Rust-based MDQ tool directly in your browser. Thank you to <a href="https://github.com/jerrylususu">@jerrylususu</a> for kickstarting this playground!</p>
                            <p>Learn more about MDQ and its selector syntax at <a href="https://github.com/yshavit/mdq" target="_blank">yshavit/mdq</a> on GitHub.</p>
                            <p>For bugs or feature requests for this web UI, see <a href="https://github.com/yshavit/mdq-playground/issues" target="_blank">yshavit/mdq-playground</a>.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>MDQ WASM Demo | Powered by Rust and WebAssembly</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.23.0/ace.js"></script>
    <script type="module" src="./dist/index.js"></script>
</body>
</html>
