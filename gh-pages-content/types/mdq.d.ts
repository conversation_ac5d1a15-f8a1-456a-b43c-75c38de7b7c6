// Enhanced type definitions for MDQ WASM module

// Re-export the generated types from the WASM module
export * from '../pkg/mdq_wasm.js';

// Additional application-specific types
export interface MdqResult {
  items: MdqItem[];
}

export interface MdqItem {
  content: string;
  type?: string;
  level?: number;
  [key: string]: unknown;
}

export type OutputFormat = 'markdown' | 'json';

export interface MdqError {
  message: string;
  code?: string;
}

// Type guard functions
export function isMdqResult(obj: unknown): obj is MdqResult {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'items' in obj &&
    Array.isArray((obj as MdqResult).items)
  );
}

export function isOutputFormat(value: string): value is OutputFormat {
  return value === 'markdown' || value === 'json';
}
