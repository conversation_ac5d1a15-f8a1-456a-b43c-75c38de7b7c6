# MDQ WASM TypeScript Demo

This directory contains the TypeScript-powered web interface for the MDQ WebAssembly demo.

## Features

✅ **Full TypeScript Support**: Complete type safety when interfacing with WASM  
✅ **Static Type Checking**: Catch errors at compile time, not runtime  
✅ **Enhanced Developer Experience**: IntelliSense, auto-completion, and refactoring support  
✅ **Type-Safe WASM Bindings**: Properly typed interfaces for all WASM functions  
✅ **Runtime Type Guards**: Additional safety with runtime type validation  

## Quick Start

### Prerequisites

- Node.js (v16 or later)
- Rust with `wasm-pack` installed

### Build and Run

1. **Build everything** (Rust WASM + TypeScript):
   ```bash
   # On Unix/Linux/macOS
   ./build.sh
   
   # On Windows
   .\build.ps1
   ```

2. **Or build step by step**:
   ```bash
   # Build Rust WASM module
   cd ..
   wasm-pack build --target web --out-dir gh-pages-content/pkg
   
   # Install TypeScript dependencies
   cd gh-pages-content
   npm install
   
   # Compile TypeScript
   npm run build
   ```

3. **Serve the demo**:
   ```bash
   npm run serve
   # or
   npx http-server -p 8080
   ```

4. **Development mode** (auto-rebuild on changes):
   ```bash
   npm run dev
   ```

## Project Structure

```
gh-pages-content/
├── index.html          # Main HTML file
├── index.ts            # TypeScript source (replaces index.js)
├── dist/               # Compiled JavaScript output
│   └── index.js        # Generated from index.ts
├── pkg/                # Generated WASM bindings
│   ├── mdq_wasm.js     # WASM JavaScript bindings
│   ├── mdq_wasm.d.ts   # Auto-generated TypeScript definitions
│   └── mdq_wasm_bg.wasm # Compiled WebAssembly module
├── types/              # Custom TypeScript definitions
│   ├── ace.d.ts        # Ace Editor types
│   └── mdq.d.ts        # Enhanced MDQ types
├── tsconfig.json       # TypeScript configuration
├── package.json        # Node.js dependencies and scripts
└── build.sh/.ps1       # Build scripts
```

## TypeScript Benefits

### 1. **Type-Safe WASM Interface**

Before (JavaScript):
```javascript
// No type checking - errors only at runtime
const options = new MdqOptions();
options.set_output_format("invalid-format"); // Runtime error
const result = process_markdown(content, selector, options);
```

After (TypeScript):
```typescript
// Full type safety - errors caught at compile time
const options: MdqOptions = new MdqOptions();
options.set_output_format(outputFormat); // Type-checked
const result: string = process_markdown(content, selector, options);
```

### 2. **Enhanced Error Handling**

```typescript
// Type guards for runtime safety
if (isMdqResult(jsonObj)) {
  // TypeScript knows jsonObj is MdqResult
  console.log(`Found ${jsonObj.items.length} items`);
}

// Type-safe output format validation
if (!isOutputFormat(userInput)) {
  throw new Error(`Invalid format: ${userInput}`);
}
```

### 3. **Better Developer Experience**

- **IntelliSense**: Auto-completion for all WASM functions and types
- **Refactoring**: Safe renaming and code restructuring
- **Documentation**: Hover hints show function signatures and documentation
- **Error Prevention**: Catch typos and type mismatches before runtime

## Available Scripts

- `npm run build` - Compile TypeScript to JavaScript
- `npm run build:watch` - Watch mode for development
- `npm run serve` - Start local HTTP server
- `npm run dev` - Development mode (watch + serve)
- `npm run clean` - Remove compiled output
- `npm run type-check` - Type check without emitting files

## Type Definitions

### Core WASM Types (Auto-generated)

```typescript
// From pkg/mdq_wasm.d.ts
export class MdqOptions {
  constructor();
  output_format(): string;
  set_output_format(format: string): void;
  set_wrap_width(width?: number | null): void;
  set_quiet(quiet: boolean): void;
}

export function process_markdown(
  markdown_content: string, 
  selector: string, 
  options: MdqOptions
): string;
```

### Enhanced Application Types

```typescript
// From types/mdq.d.ts
export interface MdqResult {
  items: MdqItem[];
}

export interface MdqItem {
  content: string;
  type?: string;
  level?: number;
  [key: string]: unknown;
}

export type OutputFormat = 'markdown' | 'json';
```

## Migration from JavaScript

The TypeScript version is a drop-in replacement for the JavaScript version with these improvements:

1. **Type Safety**: All variables and function parameters are properly typed
2. **Error Handling**: Enhanced error handling with type guards
3. **Code Quality**: Stricter null checks and better error messages
4. **Maintainability**: Self-documenting code with explicit types

The functionality remains identical - only the developer experience and safety are improved.
