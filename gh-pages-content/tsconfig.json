{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true}, "include": ["*.ts", "*.js", "types/**/*"], "exclude": ["node_modules", "dist", "pkg"]}