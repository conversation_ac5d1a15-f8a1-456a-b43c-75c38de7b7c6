#!/bin/bash

# Build script for MDQ WASM TypeScript Demo

set -e

echo "🦀 Building Rust WASM module..."
cd ..
wasm-pack build --target web --out-dir gh-pages-content/pkg

echo "📦 Installing TypeScript dependencies..."
cd gh-pages-content
npm install

echo "🔧 Compiling TypeScript..."
npm run build

echo "✅ Build complete! You can now serve the demo with:"
echo "   npm run serve"
echo "   or"
echo "   npx http-server -p 8080"
