// Import our WASM module with proper types
import init, { process_markdown, MdqOptions } from './pkg/mdq_wasm.js';
import type { MdqResult, OutputFormat } from './types/mdq.js';
import { isMdqResult, isOutputFormat } from './types/mdq.js';

// Sample markdown for the demo
const sampleMarkdown = `# MDQ Demo Document

This is a paragraph with some **bold** and *italic* text.

## Second Level Heading

- List item 1
- List item 2
  - Nested list item
- List item 3

### Task Lists

- [ ] Uncompleted task
- [x] Completed task
- [ ] Another task to do

## Ordered Lists

1. First item
2. Second item
3. Third item

### Code Example

\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

\`\`\`rust
fn main() {
    println!("Hello from Rust!");
}
\`\`\`

Here's a [link to MDQ](https://github.com/yshavit/mdq) repository.

#### Tables

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1    | Data     | Data     |
| Row 2    | Data     | Data     |

##### Blockquote

> This is a blockquote.
> It can span multiple lines.

###### Images

![MDQ Logo](https://via.placeholder.com/150)

`;

// Global variables with proper types
let editor: ace.Editor;

// Helper function to safely get element by ID with type checking
function getElementById<T extends HTMLElement>(id: string): T {
  const element = document.getElementById(id) as T | null;
  if (!element) {
    throw new Error(`Element with id '${id}' not found`);
  }
  return element;
}

// Helper function to safely query selector with type checking
function querySelectorAll<T extends Element>(selector: string): NodeListOf<T> {
  return document.querySelectorAll<T>(selector);
}

// Initialize our application when the page loads
document.addEventListener('DOMContentLoaded', async (): Promise<void> => {
  try {
    // Initialize the WASM module
    await init();
    console.log('WASM module initialized successfully');
    
    // Set up the Ace editor
    editor = ace.edit("markdown-editor");
    editor.setTheme("ace/theme/github");
    editor.session.setMode("ace/mode/markdown");
    editor.setValue(sampleMarkdown);
    editor.clearSelection();
    
    // Add event listeners
    const processBtn = getElementById<HTMLButtonElement>('process-btn');
    processBtn.addEventListener('click', processMarkdown);
    
    // Add event listeners for example selectors
    const exampleSelectors = querySelectorAll<HTMLElement>('.example-selector');
    exampleSelectors.forEach((elem: HTMLElement) => {
      elem.addEventListener('click', () => {
        const selector = elem.dataset.selector;
        if (selector) {
          const selectorInput = getElementById<HTMLInputElement>('selector');
          selectorInput.value = selector;
          processMarkdown();
        }
      });
    });
    
    // Process with default selector
    const selectorInput = getElementById<HTMLInputElement>('selector');
    selectorInput.value = '# heading';
    await processMarkdown();
    
  } catch (error) {
    console.error('Failed to initialize WASM module:', error);
    const resultElement = getElementById<HTMLDivElement>('result');
    const errorMessage = error instanceof Error ? error.message : String(error);
    resultElement.innerHTML = `<div class="alert alert-danger">
      Failed to initialize WASM module: ${errorMessage}
    </div>`;
  }
});

// Process the markdown with the given selector
async function processMarkdown(): Promise<void> {
  const markdownContent: string = editor.getValue();
  const selectorInput = getElementById<HTMLInputElement>('selector');
  const selector: string = selectorInput.value;
  const outputFormatSelect = getElementById<HTMLSelectElement>('output-format');
  const outputFormatValue = outputFormatSelect.value;
  if (!isOutputFormat(outputFormatValue)) {
    throw new Error(`Invalid output format: ${outputFormatValue}`);
  }
  const outputFormat: OutputFormat = outputFormatValue;
  
  if (!markdownContent || !selector) {
    const resultElement = getElementById<HTMLDivElement>('result');
    resultElement.innerHTML = '<div class="alert alert-warning">Please provide both markdown content and a selector.</div>';
    return;
  }
  
  // Show loading indicator
  const loadingElement = getElementById<HTMLDivElement>('loading');
  const processBtn = getElementById<HTMLButtonElement>('process-btn');
  loadingElement.style.display = 'inline-block';
  processBtn.disabled = true;
  
  try {
    // Create options object with proper typing
    const options: MdqOptions = new MdqOptions();
    options.set_output_format(outputFormat);
    
    // Process the markdown - this returns a string, but we need to handle potential errors
    const result: string = process_markdown(markdownContent, selector, options);
    
    // Check if the result is exactly '{"items":[]}'
    if (result === '{"items":[]}') {
      const resultElement = getElementById<HTMLDivElement>('result');
      resultElement.innerHTML = `<pre>No matches found.</pre>`;
      return;
    }
    
    // Display the result
    const resultElement = getElementById<HTMLDivElement>('result');
    
    if (outputFormat === 'json') {
      // Format JSON for better display with proper error handling
      try {
        const jsonObj: unknown = JSON.parse(result);

        // Type-safe check for MdqResult
        if (isMdqResult(jsonObj)) {
          // Check if no matches were found (empty items array)
          if (jsonObj.items.length === 0) {
            resultElement.innerHTML = `<pre>No matches found.</pre>`;
          } else {
            resultElement.innerHTML = `<pre>${JSON.stringify(jsonObj, null, 2)}</pre>`;
          }
        } else {
          // Fallback for unexpected JSON structure
          resultElement.innerHTML = `<pre>${JSON.stringify(jsonObj, null, 2)}</pre>`;
        }
      } catch (parseError) {
        console.warn('Failed to parse JSON result, displaying as plain text:', parseError);
        resultElement.innerHTML = `<pre>${result}</pre>`;
      }
    } else {
      // For markdown, display as preformatted text
      resultElement.innerHTML = `<pre>${result}</pre>`;
    }
  } catch (error) {
    console.error('Error processing markdown:', error);
    
    // Handle the case where error is a string or Error object
    const errorStr: string = typeof error === 'string' ? error : 
                            error instanceof Error ? error.message : 
                            String(error);
    
    // Check if the error string contains '{"items":[]}'
    const resultElement = getElementById<HTMLDivElement>('result');
    if (errorStr.includes('{"items":[]}')) {
      resultElement.innerHTML = `<pre>No matches found.</pre>`;
    } else {
      resultElement.innerHTML = `<div class="alert alert-danger">
        Error processing markdown: ${errorStr}
      </div>`;
    }
  } finally {
    // Hide loading indicator
    const loadingElement = getElementById<HTMLDivElement>('loading');
    const processBtn = getElementById<HTMLButtonElement>('process-btn');
    loadingElement.style.display = 'none';
    processBtn.disabled = false;
  }
}
