# PowerShell build script for MDQ WASM TypeScript Demo

Write-Host "🦀 Building Rust WASM module..." -ForegroundColor Green
Set-Location ..
wasm-pack build --target web --out-dir gh-pages-content/pkg

Write-Host "📦 Installing TypeScript dependencies..." -ForegroundColor Green
Set-Location gh-pages-content
npm install

Write-Host "🔧 Compiling TypeScript..." -ForegroundColor Green
npm run build

Write-Host "✅ Build complete! You can now serve the demo with:" -ForegroundColor Green
Write-Host "   npm run serve" -ForegroundColor Yellow
Write-Host "   or" -ForegroundColor Yellow
Write-Host "   npx http-server -p 8080" -ForegroundColor Yellow
