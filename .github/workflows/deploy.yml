# Simple workflow for deploying static content to GitHub Pages
name: Deploy to Pages

on:
  # Runs on pushes targeting the default branch
  push:
    branches: ["main"]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Single deploy job since we're just deploying
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - uses: taiki-e/install-action@v2
        with:
          tool: wasm-pack
      - run: wasm-pack build --target web -d gh-pages-content/pkg
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: gh-pages-content
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
